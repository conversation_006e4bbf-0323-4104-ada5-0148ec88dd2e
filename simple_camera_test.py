#!/usr/bin/env python3
"""
Simple camera test to check if camera feed is working
"""
import cv2

def test_camera_feed():
    """Test basic camera feed without face recognition"""
    print("Testing camera feed...")
    print("Press 'q' to quit")

    # Try camera index 1 first (based on troubleshooting results)
    cap = cv2.VideoCapture(1)

    if not cap.isOpened():
        print("Camera 1 failed, trying camera 0...")
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Error: Could not open any camera")
            return
    
    # Set camera properties for better performance
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)

    # Give camera time to initialize
    import time
    time.sleep(1)

    frame_count = 0
    while True:
        ret, frame = cap.read()

        if not ret:
            print(f"Error: Failed to capture frame {frame_count}")
            break

        # Check if frame is valid
        if frame is None or frame.size == 0:
            print("Invalid frame received")
            continue

        # Check for green screen issue
        if len(frame.shape) == 3:
            mean_color = frame.mean(axis=(0,1))
            if mean_color[1] > 200 and mean_color[0] < 50 and mean_color[2] < 50:
                print(f"Frame {frame_count}: Green screen detected!")
                # Continue showing it but with warning

        frame_count += 1
        
        # Add some text to verify the frame is being processed
        cv2.putText(frame, f"Camera Test - Frame {frame_count} - Press 'q' to quit", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # Add frame info
        cv2.putText(frame, f"Size: {frame.shape[1]}x{frame.shape[0]}", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        cv2.imshow("Camera Test", frame)
        
        # Press 'q' to quit
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
    print("Camera test completed")

if __name__ == "__main__":
    test_camera_feed()
