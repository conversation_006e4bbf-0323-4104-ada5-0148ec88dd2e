#!/usr/bin/env python3
"""
Test script to verify attendance fixes
"""
import os
import time
from datetime import datetime
from face_recognition import Face_Recognition
from tkinter import Tk

def test_attendance_no_duplicates():
    """Test that attendance prevents duplicates"""
    print("Testing attendance duplicate prevention...")
    
    # Remove existing attendance file for clean test
    if os.path.exists("attendance.csv"):
        os.remove("attendance.csv")
        print("Removed existing attendance.csv")
    
    # Create Face_Recognition instance
    root = Tk()
    root.withdraw()  # Hide the main window
    face_rec = Face_Recognition(root)
    
    # Test marking attendance multiple times for same student
    print("\nTest 1: Mark attendance for <PERSON>")
    face_rec.mark_attendance("<PERSON>", "CS001", "Computer Science", "1")
    
    print("\nTest 2: Try to mark attendance for <PERSON> again (should be blocked)")
    face_rec.mark_attendance("John Doe", "CS001", "Computer Science", "1")
    
    print("\nTest 3: Mark attendance for different student")
    face_rec.mark_attendance("<PERSON>", "CS002", "Computer Science", "2")
    
    print("\nTest 4: Try <PERSON> again after 2 seconds (should still be blocked)")
    time.sleep(2)
    face_rec.mark_attendance("John <PERSON>e", "CS001", "Computer Science", "1")
    
    # Check the CSV file
    print("\n" + "="*50)
    print("FINAL ATTENDANCE RECORDS:")
    print("="*50)
    
    try:
        with open("attendance.csv", "r") as f:
            lines = f.readlines()
            for i, line in enumerate(lines):
                print(f"{i+1}: {line.strip()}")
            
            # Count actual data rows (excluding header)
            data_rows = len([line for line in lines if line.strip() and not line.startswith("Name,")])
            print(f"\nTotal attendance records: {data_rows}")
            
            if data_rows == 2:
                print("✓ SUCCESS: Only unique students recorded (no duplicates)")
            else:
                print("✗ FAILED: Duplicates found or incorrect count")
                
    except FileNotFoundError:
        print("✗ No attendance file found!")
    
    root.destroy()

def check_csv_format():
    """Check CSV format"""
    print("\n" + "="*50)
    print("CSV FORMAT CHECK:")
    print("="*50)
    
    if not os.path.exists("attendance.csv"):
        print("No attendance.csv file found")
        return
    
    with open("attendance.csv", "r") as f:
        lines = f.readlines()
    
    if len(lines) == 0:
        print("Empty file")
        return
    
    # Check header
    if lines[0].strip() == "Name,Roll,Department,Student_ID,Time,Date,Status":
        print("✓ Header format is correct")
    else:
        print(f"✗ Header format incorrect: {lines[0].strip()}")
    
    # Check data format
    for i, line in enumerate(lines[1:], 2):
        if line.strip():
            parts = line.strip().split(",")
            if len(parts) == 7:
                print(f"✓ Row {i}: Correct format with 7 fields")
            else:
                print(f"✗ Row {i}: Wrong format - {len(parts)} fields instead of 7")

if __name__ == "__main__":
    print("Attendance System Fix Test")
    print("=" * 50)
    
    test_attendance_no_duplicates()
    check_csv_format()
    
    print("\n" + "="*50)
    print("CAMERA FIX VERIFICATION")
    print("="*50)
    print("Camera 1 should be used instead of Camera 0")
    print("When you click 'FACE RECOGNITION' button:")
    print("1. Console should show 'Trying camera 0...'")
    print("2. Then 'Camera 0 opened but can't read frames'") 
    print("3. Then 'Trying camera 1...'")
    print("4. Then '✓ Camera 1 works!'")
    print("5. Camera window should open with your video feed (not green screen)")
    
    print("\n" + "="*50)
    print("EXPECTED BEHAVIOR")
    print("="*50)
    print("✓ Camera opens with real video feed (no green screen)")
    print("✓ Only one attendance record per student per day")
    print("✓ 30-second cooldown prevents rapid duplicates")
    print("✓ Unknown faces show 'Unknown Face' text")
    print("✓ Known faces show student information")
    print("✓ CSV has proper header and format")
