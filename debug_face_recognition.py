import cv2
import mysql.connector
import numpy as np

def debug_face_recognition():
    print("=== FACE RECOGNITION DEBUG TOOL ===\n")
    
    # 1. Check if classifier file exists and loads properly
    try:
        clf = cv2.face.LBPHFaceRecognizer_create()
        clf.read("classifier.xml")
        print("✅ Classifier file loaded successfully")
    except Exception as e:
        print(f"❌ Error loading classifier: {e}")
        return
    
    # 2. Check database connection and student data
    try:
        conn = mysql.connector.connect(
            host="localhost", 
            username="root", 
            password="Admin@1402", 
            database="face_recognizer"
        )
        cursor = conn.cursor()
        
        # Get all students
        cursor.execute("SELECT student_id, Name, Roll, Dep FROM student")
        students = cursor.fetchall()
        
        print(f"✅ Database connected successfully")
        print(f"📊 Found {len(students)} students in database:")
        for student in students:
            print(f"   ID: {student[0]}, Name: {student[1]}, Roll: {student[2]}, Dept: {student[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return
    
    # 3. Test face recognition with camera
    print(f"\n🎥 Testing face recognition...")
    print("Press 'q' to quit, 's' to show detailed info for current detection")
    
    # Load face cascade
    face_cascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
    if face_cascade.empty():
        print("❌ Face cascade file not found!")
        return
    
    # Open camera
    cap = cv2.VideoCapture(1)
    if not cap.isOpened():
        cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ Cannot open camera!")
        return
    
    print("✅ Camera opened successfully")
    
    frame_count = 0
    detection_stats = {}
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        frame_count += 1
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 5)
        
        for (x, y, w, h) in faces:
            # Predict face
            id, confidence_raw = clf.predict(gray[y:y+h, x:x+w])
            confidence = int(100 * (1 - confidence_raw / 300))
            
            # Track detection statistics
            if id not in detection_stats:
                detection_stats[id] = []
            detection_stats[id].append(confidence)
            
            # Draw rectangle and info
            color = (0, 255, 0) if confidence > 77 else (0, 255, 255) if confidence > 50 else (0, 0, 255)
            cv2.rectangle(frame, (x, y), (x+w, y+h), color, 2)
            cv2.putText(frame, f"ID: {id}", (x, y-40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            cv2.putText(frame, f"Conf: {confidence}%", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # Show detailed info every 30 frames
            if frame_count % 30 == 0:
                print(f"Frame {frame_count}: Detected ID={id}, Confidence={confidence}%")
        
        cv2.imshow("Face Recognition Debug", frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # Show detailed statistics
            print(f"\n📊 DETECTION STATISTICS (Frame {frame_count}):")
            for student_id, confidences in detection_stats.items():
                avg_conf = sum(confidences) / len(confidences)
                max_conf = max(confidences)
                min_conf = min(confidences)
                print(f"   ID {student_id}: Avg={avg_conf:.1f}%, Max={max_conf}%, Min={min_conf}%, Count={len(confidences)}")
    
    cap.release()
    cv2.destroyAllWindows()
    
    # Final statistics
    print(f"\n📈 FINAL STATISTICS:")
    for student_id, confidences in detection_stats.items():
        avg_conf = sum(confidences) / len(confidences)
        above_77 = len([c for c in confidences if c > 77])
        above_50 = len([c for c in confidences if c > 50])
        print(f"   ID {student_id}: Avg={avg_conf:.1f}%, Above 77%: {above_77}/{len(confidences)}, Above 50%: {above_50}/{len(confidences)}")

if __name__ == "__main__":
    debug_face_recognition()
