#!/usr/bin/env python3
"""
Test script to verify attendance marking functionality
"""
import os
import time
from datetime import datetime
from face_recognition import Face_Recognition
from tkinter import Tk

def test_attendance_marking():
    """Test the attendance marking functionality"""
    print("Testing attendance marking...")
    
    # Remove existing attendance file for clean test
    if os.path.exists("attendance.csv"):
        os.remove("attendance.csv")
        print("Removed existing attendance.csv")
    
    # Create Face_Recognition instance
    root = Tk()
    root.withdraw()  # Hide the main window
    face_rec = Face_Recognition(root)
    
    # Test data
    test_students = [
        ("John Doe", "CS001", "Computer Science", "1"),
        ("<PERSON>", "CS002", "Computer Science", "2"),
        ("<PERSON> Doe", "CS001", "Computer Science", "1"),  # Duplicate same day
        ("<PERSON> Wilson", "EE001", "Electrical Engineering", "3"),
    ]
    
    print("\nMarking attendance for test students...")
    
    for i, (name, roll, dept, student_id) in enumerate(test_students):
        print(f"\nTest {i+1}: Marking attendance for {name} (ID: {student_id})")
        face_rec.mark_attendance(name, roll, dept, student_id)
        
        # Add small delay between tests
        if i == 1:  # After second student
            print("Waiting 2 seconds...")
            time.sleep(2)
    
    # Read and display the attendance file
    print("\n" + "="*50)
    print("ATTENDANCE RECORDS:")
    print("="*50)
    
    try:
        with open("attendance.csv", "r") as f:
            lines = f.readlines()
            for i, line in enumerate(lines):
                print(f"{i+1}: {line.strip()}")
    except FileNotFoundError:
        print("No attendance file found!")
    
    # Test duplicate detection
    print("\n" + "="*50)
    print("TESTING DUPLICATE DETECTION:")
    print("="*50)
    
    print("Trying to mark attendance for John Doe again (should be blocked)...")
    face_rec.mark_attendance("John Doe", "CS001", "Computer Science", "1")
    
    print("\nFinal attendance records:")
    try:
        with open("attendance.csv", "r") as f:
            lines = f.readlines()
            print(f"Total records: {len(lines) - 1}")  # -1 for header
            for i, line in enumerate(lines):
                print(f"{i+1}: {line.strip()}")
    except FileNotFoundError:
        print("No attendance file found!")
    
    root.destroy()

def check_csv_format():
    """Check if the CSV file has proper format"""
    print("\n" + "="*50)
    print("CSV FORMAT CHECK:")
    print("="*50)
    
    if not os.path.exists("attendance.csv"):
        print("No attendance.csv file found")
        return
    
    with open("attendance.csv", "r") as f:
        lines = f.readlines()
    
    if len(lines) == 0:
        print("Empty file")
        return
    
    # Check header
    header = lines[0].strip().split(",")
    expected_header = ["Name", "Roll", "Department", "Student_ID", "Time", "Date", "Status"]
    
    print(f"Header: {header}")
    print(f"Expected: {expected_header}")
    print(f"Header correct: {header == expected_header}")
    
    # Check data rows
    for i, line in enumerate(lines[1:], 2):
        if line.strip():
            parts = line.strip().split(",")
            print(f"Row {i}: {len(parts)} fields - {parts}")
            if len(parts) != 7:
                print(f"  ⚠️  Row {i} has {len(parts)} fields, expected 7")

if __name__ == "__main__":
    test_attendance_marking()
    check_csv_format()
    
    print("\n" + "="*50)
    print("TEST COMPLETED")
    print("="*50)
    print("Expected behavior:")
    print("1. Only unique students per day should be recorded")
    print("2. Duplicate attempts should be blocked")
    print("3. CSV should have proper format with header")
    print("4. 30-second cooldown should prevent rapid duplicates")
