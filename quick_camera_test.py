import cv2

# Test different camera indices
for i in range(3):
    print(f"Testing camera {i}...")
    cap = cv2.VideoCapture(i)
    if cap.isOpened():
        ret, frame = cap.read()
        if ret and frame is not None:
            print(f"✓ Camera {i} works! Frame size: {frame.shape}")
            cap.release()
            break
        else:
            print(f"✗ Camera {i} opened but can't read frames")
    else:
        print(f"✗ Camera {i} failed to open")
    cap.release()
