#!/usr/bin/env python3
"""
Test script to verify face recognition functionality
"""
import cv2
import os
import mysql.connector
from tkinter import messagebox as msgbox

def test_files():
    """Test if required files exist"""
    print("Testing required files...")
    
    # Check haarcascade file
    if os.path.exists("haarcascade_frontalface_default.xml"):
        print("✓ haarcascade_frontalface_default.xml found")
    else:
        print("✗ haarcascade_frontalface_default.xml NOT found")
        return False
    
    # Check classifier file
    if os.path.exists("classifier.xml"):
        print("✓ classifier.xml found")
    else:
        print("✗ classifier.xml NOT found")
        return False
    
    return True

def test_camera():
    """Test camera access"""
    print("\nTesting camera access...")
    cap = cv2.VideoCapture(0)
    if cap.isOpened():
        print("✓ Camera accessible")
        cap.release()
        return True
    else:
        print("✗ Camera NOT accessible")
        return False

def test_database():
    """Test database connection"""
    print("\nTesting database connection...")
    try:
        conn = mysql.connector.connect(
            host="localhost", 
            username="root", 
            password="Admin@1402", 
            database="face_recognizer"
        )
        print("✓ Database connection successful")
        
        # Test if student table exists
        cursor = conn.cursor()
        cursor.execute("SHOW TABLES LIKE 'student'")
        result = cursor.fetchone()
        if result:
            print("✓ Student table exists")
            
            # Check if there are any students
            cursor.execute("SELECT COUNT(*) FROM student")
            count = cursor.fetchone()[0]
            print(f"✓ Found {count} students in database")
        else:
            print("✗ Student table NOT found")
            
        conn.close()
        return True
    except mysql.connector.Error as err:
        print(f"✗ Database connection failed: {err}")
        return False

def test_opencv_face_detection():
    """Test OpenCV face detection"""
    print("\nTesting OpenCV face detection...")
    try:
        # Load cascade
        face_cascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
        if face_cascade.empty():
            print("✗ Failed to load face cascade")
            return False
        
        # Load classifier
        clf = cv2.face.LBPHFaceRecognizer_create()
        clf.read("classifier.xml")
        print("✓ Face recognition model loaded successfully")
        return True
    except Exception as e:
        print(f"✗ OpenCV face detection test failed: {e}")
        return False

if __name__ == "__main__":
    print("Face Recognition System Test")
    print("=" * 40)
    
    all_tests_passed = True
    
    # Run all tests
    all_tests_passed &= test_files()
    all_tests_passed &= test_camera()
    all_tests_passed &= test_database()
    all_tests_passed &= test_opencv_face_detection()
    
    print("\n" + "=" * 40)
    if all_tests_passed:
        print("✓ All tests PASSED! Face recognition should work properly.")
    else:
        print("✗ Some tests FAILED. Please fix the issues above.")
    
    print("\nInstructions:")
    print("1. Make sure your camera is connected and working")
    print("2. Ensure the database is running and contains student data")
    print("3. Click the 'FACE RECOGNITION' button to start")
    print("4. Press 'q' or Enter to stop face recognition")
