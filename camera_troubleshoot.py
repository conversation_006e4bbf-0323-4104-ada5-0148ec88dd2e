#!/usr/bin/env python3
"""
Comprehensive camera troubleshooting script
"""
import cv2
import numpy as np

def test_camera_backends():
    """Test different camera backends"""
    backends = [
        (cv2.CAP_DSHOW, "DirectShow"),
        (cv2.CAP_MSMF, "Microsoft Media Foundation"),
        (cv2.CAP_V4L2, "Video4Linux2"),
        (cv2.CAP_ANY, "Any/Default")
    ]
    
    print("Testing different camera backends...")
    
    for backend_id, backend_name in backends:
        print(f"\nTrying {backend_name} backend...")
        try:
            cap = cv2.VideoCapture(0, backend_id)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"✓ {backend_name} works! Frame size: {frame.shape}")
                    cap.release()
                    return backend_id, backend_name
                else:
                    print(f"✗ {backend_name} opened but can't read frames")
            else:
                print(f"✗ {backend_name} failed to open")
            cap.release()
        except Exception as e:
            print(f"✗ {backend_name} error: {e}")
    
    return None, None

def test_multiple_camera_indices():
    """Test different camera indices"""
    print("\nTesting different camera indices...")
    
    for i in range(5):  # Test indices 0-4
        print(f"Testing camera index {i}...")
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"✓ Camera {i} works! Frame size: {frame.shape}")
                    cap.release()
                    return i
                else:
                    print(f"✗ Camera {i} opened but can't read frames")
            else:
                print(f"✗ Camera {i} failed to open")
            cap.release()
        except Exception as e:
            print(f"✗ Camera {i} error: {e}")
    
    return None

def create_test_frame():
    """Create a test frame to verify display works"""
    print("\nCreating test frame...")
    
    # Create a test image
    test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Add some colors and text
    test_frame[:160, :] = [0, 0, 255]  # Red top
    test_frame[160:320, :] = [0, 255, 0]  # Green middle  
    test_frame[320:, :] = [255, 0, 0]  # Blue bottom
    
    cv2.putText(test_frame, "TEST FRAME - Press any key", (50, 240), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    cv2.imshow("Test Frame", test_frame)
    print("Test frame displayed. Press any key to continue...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def test_camera_with_settings(camera_index=0, backend=cv2.CAP_ANY):
    """Test camera with various settings"""
    print(f"\nTesting camera {camera_index} with backend...")
    
    cap = cv2.VideoCapture(camera_index, backend)
    if not cap.isOpened():
        print("Failed to open camera")
        return False
    
    # Try different resolutions
    resolutions = [(640, 480), (320, 240), (1280, 720)]
    
    for width, height in resolutions:
        print(f"Trying resolution {width}x{height}...")
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        
        ret, frame = cap.read()
        if ret and frame is not None:
            actual_height, actual_width = frame.shape[:2]
            print(f"✓ Got frame: {actual_width}x{actual_height}")
            
            # Quick test display
            cv2.putText(frame, f"Resolution Test {actual_width}x{actual_height}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.imshow("Camera Test", frame)
            cv2.waitKey(1000)  # Show for 1 second
            cv2.destroyAllWindows()
            
            cap.release()
            return True
        else:
            print(f"✗ Failed to get frame at {width}x{height}")
    
    cap.release()
    return False

def main():
    print("Camera Troubleshooting Tool")
    print("=" * 40)
    
    # Test 1: Display capability
    create_test_frame()
    
    # Test 2: Find working backend
    backend_id, backend_name = test_camera_backends()
    
    # Test 3: Find working camera index
    camera_index = test_multiple_camera_indices()
    
    # Test 4: Test with best settings found
    if camera_index is not None:
        if backend_id is not None:
            print(f"\nUsing camera {camera_index} with {backend_name} backend")
            success = test_camera_with_settings(camera_index, backend_id)
        else:
            print(f"\nUsing camera {camera_index} with default backend")
            success = test_camera_with_settings(camera_index)
        
        if success:
            print("\n✓ Camera is working!")
            print(f"Recommended settings:")
            print(f"  Camera index: {camera_index}")
            if backend_id is not None:
                print(f"  Backend: {backend_name} (cv2.{backend_id})")
        else:
            print("\n✗ Camera issues persist")
    else:
        print("\n✗ No working camera found")
    
    print("\nTroubleshooting complete.")

if __name__ == "__main__":
    main()
