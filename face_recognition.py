from tkinter import *
from tkinter import messagebox as msgbox 
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Track last attendance time for each student to prevent rapid duplicates
        self.last_attendance_time = {}

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)


        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        now = datetime.now()
        current_date = now.strftime("%d/%m/%Y")
        current_time = now.strftime("%H:%M:%S")

        # Check cooldown period (prevent marking attendance within 30 seconds)
        student_key = str(id)
        if student_key in self.last_attendance_time:
            time_diff = (now - self.last_attendance_time[student_key]).total_seconds()
            if time_diff < 30:  # 30 seconds cooldown
                return  # Skip marking attendance

        # Check if student already has attendance for today
        attendance_exists = False
        try:
            with open("attendance.csv", "r") as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip():  # Skip empty lines
                        parts = line.strip().split(",")
                        if len(parts) >= 6:  # Ensure line has enough parts
                            existing_id = parts[3]
                            existing_date = parts[5]
                            # Check if same student ID and same date
                            if existing_id == str(id) and existing_date == current_date:
                                attendance_exists = True
                                print(f"Attendance already marked for {n} (ID: {id}) today")
                                break
        except FileNotFoundError:
            # File doesn't exist yet, create it with header
            with open("attendance.csv", "w") as f:
                f.write("Name,Roll,Department,Student_ID,Time,Date,Status\n")

        # Only mark attendance if not already marked today
        if not attendance_exists:
            with open("attendance.csv", "a") as f:
                f.write(f"{n},{r},{d},{id},{current_time},{current_date},Present\n")
                print(f"✓ Attendance marked for {n} (ID: {id}) at {current_time}")
                # Update last attendance time
                self.last_attendance_time[student_key] = now

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors,color,text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)
            cord =[]
            for (x, y, w, h) in features:
                cv2.rectangle(img, (x, y), (x+w, y+h),(0, 255, 0),3)
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))
                conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                my_cursor = conn.cursor()
                my_cursor.execute ("select Student_id from student where Student_id="+str(id))
                id = my_cursor.fetchone()
                id = "+".join(id) 
                
                my_cursor.execute("select Name from student where Student_id="+str(id))
                n = my_cursor.fetchone()
                n = "+".join(n)

                my_cursor.execute("select Roll from student where Student_id="+str(id))
                r = my_cursor.fetchone()
                r = "+".join(r)
                
                my_cursor.execute("select Dep from student where Student_id="+str(id))
                d = my_cursor.fetchone()
                d = "+".join(d)
                
                if confidence > 77:
                    try:
                        cv2.putText(img, f"Student ID: {id}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Name: {n}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Roll: {r}", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Department: {d}", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        self.mark_attendance(n, r, d, id)
                    except Exception as e:
                        print(f"Error displaying student info: {e}")
                        cv2.putText(img, "Database Error", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                else:
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, "Unknown Face", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                cord = [x,y,w,h]
                
            return cord
        def recognize(img, clf, faceCascade):
            # Add status text to show the system is working
            cv2.putText(img, "Face Recognition Active - Press Enter to quit",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            cord = draw_boundary(img, faceCascade, 1.1, 10, (255, 255, 255), "Face", clf)
            return img

        try:
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Could not load haarcascade_frontalface_default.xml")
                return

            clf = cv2.face.LBPHFaceRecognizer_create()
            clf.read("classifier.xml")
        except Exception as e:
            msgbox.showerror("Error", f"Could not load classifier.xml: {str(e)}")
            return

        # Try camera index 1 first (based on testing), then fallback to 0
        video_capture = None
        for camera_index in [1, 0]:
            print(f"Trying camera {camera_index}...")
            video_capture = cv2.VideoCapture(camera_index)
            if video_capture.isOpened():
                ret, test_frame = video_capture.read()
                if ret and test_frame is not None:
                    print(f"✓ Camera {camera_index} works!")
                    break
                else:
                    print(f"Camera {camera_index} opened but can't read frames")
                    video_capture.release()
                    video_capture = None
            else:
                print(f"Camera {camera_index} failed to open")
                if video_capture:
                    video_capture.release()
                video_capture = None

        if video_capture is None:
            msgbox.showerror("Error", "Could not open any camera")
            return

        while True:
            ret, img = video_capture.read()
            if not ret:
                print("Failed to grab frame")
                break
            img = recognize(img, clf, faceCascade)
            cv2.imshow("Face Detector", img)
            if cv2.waitKey(1) == 13:
                break
        video_capture.release()
        cv2.destroyAllWindows()
    
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          