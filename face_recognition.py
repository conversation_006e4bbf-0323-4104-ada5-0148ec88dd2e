from tkinter import *
from tkinter import messagebox as msgbox 
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)


        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        with open("attendance.csv", "a+", newline="\n") as f:
            myDataList = f.readlines()
            name_list = []
            for line in myDataList:
                entry = line.split((", "))
                name_list.append(entry[0])
            if (n not in name_list) and (r not in name_list) and (d not in name_list) and (id not in name_list):
                now = datetime.now()
                d1 = now.strftime("%d/%m/%Y")
                dtString = now.strftime("%H:%M:%S")
                f.writelines(f"\n{n},{r},{d},{id},{dtString},{d1},Present")

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors, color, text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)
            cord = []

            # Debug: Print number of faces detected
            if len(features) > 0:
                print(f"Detected {len(features)} face(s)")

            for (x, y, w, h) in features:
                cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))

                if confidence > 77:
                    try:
                        # Connect to database and fetch student details
                        conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                        my_cursor = conn.cursor()

                        my_cursor.execute("select Student_id from student where Student_id=%s", (str(id),))
                        student_id_result = my_cursor.fetchone()

                        if student_id_result:
                            student_id = "+".join(map(str, student_id_result))

                            my_cursor.execute("select Name from student where Student_id=%s", (str(id),))
                            name_result = my_cursor.fetchone()
                            name = "+".join(name_result) if name_result else "Unknown"

                            my_cursor.execute("select Roll from student where Student_id=%s", (str(id),))
                            roll_result = my_cursor.fetchone()
                            roll = "+".join(roll_result) if roll_result else "Unknown"

                            my_cursor.execute("select Dep from student where Student_id=%s", (str(id),))
                            dep_result = my_cursor.fetchone()
                            department = "+".join(dep_result) if dep_result else "Unknown"

                            # Display student information
                            cv2.putText(img, f"Student ID: {student_id}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Name: {name}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Roll: {roll}", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Department: {department}", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            self.mark_attendance(name, roll, department, student_id)
                        else:
                            # Student not found in database
                            cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                            cv2.putText(img, "Unknown Face", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)

                        conn.close()

                    except mysql.connector.Error as err:
                        print(f"Database error: {err}")
                        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                        cv2.putText(img, "Database Error", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                else:
                    # Low confidence - unknown face
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, "Unknown Face", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)

                cord = [x, y, w, h]

            return cord
        def recognize(img, clf, faceCascade):
            # Add status text to show the system is working
            cv2.putText(img, "Face Recognition Active - Press 'q' or Enter to quit",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            cord = draw_boundary(img, faceCascade, 1.1, 10, (255, 255, 255), "Face", clf)
            return img

        try:
            # Load face cascade classifier
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Could not load haarcascade_frontalface_default.xml")
                return

            # Load trained classifier
            clf = cv2.face.LBPHFaceRecognizer_create()
            try:
                clf.read("classifier.xml")
            except cv2.error:
                msgbox.showerror("Error", "Could not load classifier.xml. Please train the model first.")
                return

            # Initialize video capture - try camera index 1 first (common on Windows)
            video_capture = None
            for camera_index in [1, 0]:  # Try camera 1 first, then 0
                print(f"Trying camera index {camera_index}...")
                video_capture = cv2.VideoCapture(camera_index)
                if video_capture.isOpened():
                    ret, test_frame = video_capture.read()
                    if ret and test_frame is not None:
                        print(f"✓ Camera {camera_index} works!")
                        break
                    else:
                        print(f"Camera {camera_index} opened but can't read frames")
                        video_capture.release()
                        video_capture = None
                else:
                    print(f"Camera {camera_index} failed to open")
                    if video_capture:
                        video_capture.release()
                    video_capture = None

            if video_capture is None:
                msgbox.showerror("Error", "Could not open any camera")
                return

            # Set camera properties for better performance
            video_capture.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            video_capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            video_capture.set(cv2.CAP_PROP_FPS, 30)

            # Give camera time to initialize
            import time
            time.sleep(1)

            frame_count = 0
            while True:
                ret, img = video_capture.read()
                if not ret:
                    print(f"Failed to grab frame {frame_count}")
                    # Try to reinitialize camera
                    video_capture.release()
                    video_capture = cv2.VideoCapture(0)  # Try default backend first
                    if not video_capture.isOpened():
                        print("Could not reinitialize camera")
                        break
                    continue

                # Check if frame is valid (not empty and not all green)
                if img is None or img.size == 0:
                    print("Invalid frame received")
                    continue

                # Check if frame is all green (common camera issue)
                if len(img.shape) == 3:
                    mean_color = img.mean(axis=(0,1))
                    if mean_color[1] > 200 and mean_color[0] < 50 and mean_color[2] < 50:
                        print("Green screen detected, skipping frame")
                        continue

                frame_count += 1
                img = recognize(img, clf, faceCascade)
                cv2.imshow("Face Detector", img)

                # Press Enter (13) or 'q' to quit
                key = cv2.waitKey(1) & 0xFF
                if key == 13 or key == ord('q'):
                    break

        except Exception as e:
            msgbox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            if 'video_capture' in locals():
                video_capture.release()
            cv2.destroyAllWindows()
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          